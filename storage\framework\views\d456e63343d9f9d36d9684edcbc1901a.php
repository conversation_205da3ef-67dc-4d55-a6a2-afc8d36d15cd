<?php $__env->startSection('contentho'); ?>
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12 mb-3">
            <div class="bgwhite shadow-kit rounded-lg p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="font-weight-bold">Detail Penawaran</h4>
                    <div>
                        <a href="<?php echo e(route('warehouse.penawaran.index')); ?>" class="btn btn-secondary">
                            <i class="mdi mdi-arrow-left"></i> Kembali
                        </a>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th class="bg-light" width="30%">Nomor Penawaran</th>
                                <td><?php echo e($penawaran->nomor); ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Perihal</th>
                                <td><?php echo e($penawaran->perihal); ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Customer</th>
                                <td><?php echo e($penawaran->customer); ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Attn</th>
                                <td><?php echo e($penawaran->attn ?: '-'); ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Lokasi</th>
                                <td><?php echo e($penawaran->lokasi); ?></td>
                            </tr>
                            <tr>
                                <th class="bg-light">Tanggal</th>
                                <td><?php echo e($penawaran->created_at->format('d/m/Y')); ?></td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="shadow-kit h-100">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Status Penawaran</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <h6>Status:</h6>
                                    <?php
                                        $statusClass = '';
                                        switch($penawaran->status) {
                                            case 'Draft':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Dikirim ke customer':
                                                $statusClass = 'bg-info';
                                                break;
                                            case 'PO customer':
                                                $statusClass = 'bg-primary';
                                                break;
                                            case 'Proses penyediaan':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Selesai':
                                                $statusClass = 'bg-success';
                                                break;
                                        }
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?> text-white"><?php echo e($penawaran->status); ?></span>
                                    <p class="text-muted mt-2 small">Status penawaran hanya dapat diubah oleh Sales</p>
                                </div>

                                <hr>

                                <div class="mt-3">
                                    <h6>Notes:</h6>
                                    <?php if($penawaran->notes): ?>
                                        <?php if(strpos($penawaran->notes, "\n") !== false): ?>
                                            <ul>
                                                <?php $__currentLoopData = explode("\n", $penawaran->notes); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $note): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if(trim($note) !== ''): ?>
                                                        <li><?php echo e($note); ?></li>
                                                    <?php endif; ?>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </ul>
                                        <?php else: ?>
                                            <p><?php echo e($penawaran->notes); ?></p>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <p class="text-muted">Tidak ada notes</p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12">
            <div class="bgwhite shadow-kit rounded-lg p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="font-weight-bold">Daftar Part <span class="badge bg-info">Update Status Disini</span></h4>
                    <div>
                        <form action="<?php echo e(route('warehouse.penawaran.update-all-items-status', $penawaran->id)); ?>" method="POST" class="d-inline">
                            <?php echo csrf_field(); ?>
                            <div class="input-group">
                                <select name="status" class="form-select">
                                    <option value="Ready">Ready</option>
                                    <option value="In Order">In Order</option>
                                    <option value="Not Ready">Not Ready</option>
                                </select>
                                <button type="submit" class="btn btn-primary">Update Semua</button>
                            </div>
                        </form>
                        <div class="text-muted small mt-2">
                            <i class="mdi mdi-information-outline"></i> Periksa ketersediaan stock sebelum mengubah status
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>No</th>
                                <th>Part Code</th>
                                <th>Part Name</th>
                                <th>Quantity</th>
                                <th>Stock</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $grandTotal = 0; ?>
                            <?php $__currentLoopData = $penawaran->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $total = $item->quantity * $item->price;
                                $grandTotal += $total;
                            ?>
                            <tr>
                                <td><?php echo e($index + 1); ?></td>
                                <td><?php echo e($item->partInventory->part_code); ?></td>
                                <td>
                                    <div><?php echo e($item->partInventory->part->part_name); ?></div>
                                    <?php if($item->partInventory->site_part_name): ?>
                                        <small class="text-muted"><?php echo e($item->partInventory->site_part_name); ?></small>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($item->quantity); ?></td>
                                <td>
                                    <?php
                                        $stockClass = $item->partInventory->stock_quantity >= $item->quantity ? 'text-success' : 'text-danger';
                                    ?>
                                    <span class="<?php echo e($stockClass); ?> font-weight-bold"><?php echo e($item->partInventory->stock_quantity); ?></span>
                                    <?php if($item->partInventory->stock_quantity < $item->quantity): ?>
                                        <span class="badge bg-danger">Kurang</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                        $statusClass = '';
                                        switch($item->status) {
                                            case 'Ready':
                                                $statusClass = 'bg-success';
                                                break;
                                            case 'In Order':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Not Ready':
                                                $statusClass = 'bg-danger';
                                                break;
                                        }
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?> text-white"><?php echo e($item->status); ?></span>
                                </td>
                                <td>
                                    <form action="<?php echo e(route('warehouse.penawaran.update-item-status', $penawaran->id)); ?>" method="POST">
                                        <?php echo csrf_field(); ?>
                                        <input type="hidden" name="item_id" value="<?php echo e($item->id); ?>"
                                        <?php if(app()->environment('local')): ?>
                                        data-debug="Item ID: <?php echo e($item->id); ?>"
                                        <?php endif; ?>
                                        >
                                        <div class="input-group input-group-sm">
                                            <select name="status" class="form-select form-select-sm">
                                                <option value="Ready" <?php echo e($item->status == 'Ready' ? 'selected' : ''); ?>>Ready</option>
                                                <option value="In Order" <?php echo e($item->status == 'In Order' ? 'selected' : ''); ?>>In Order</option>
                                                <option value="Not Ready" <?php echo e($item->status == 'Not Ready' ? 'selected' : ''); ?>>Not Ready</option>
                                            </select>
                                            <button type="submit" class="btn btn-sm btn-primary">Update</button>
                                        </div>
                                    </form>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<?php if(session('success')): ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            icon: 'success',
            title: 'Berhasil',
            text: "<?php echo e(session('success')); ?>",
        });
    });
</script>
<?php endif; ?>

<?php if(session('error')): ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: "<?php echo e(session('error')); ?>",
        });
    });
</script>
<?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/penawaran/show.blade.php ENDPATH**/ ?>