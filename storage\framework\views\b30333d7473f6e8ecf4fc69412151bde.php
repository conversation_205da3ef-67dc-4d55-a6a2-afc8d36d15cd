<?php $__env->startSection('contentho'); ?>
<div class="container-fluid mt-3">
    <div class="row">
        <div class="col-12">
            <div class="bgwhite shadow-kit rounded-lg p-3">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="font-weight-bold">Daftar Penawaran </h4>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="bg-primary text-white">
                            <tr>
                                <th>No</th>
                                <th>Nomor Penawaran</th>
                                <th>Perihal</th>
                                <th>Customer</th>
                                <th>Tanggal</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $penawarans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $penawaran): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($index + 1); ?></td>
                                <td><?php echo e($penawaran->nomor); ?></td>
                                <td><?php echo e($penawaran->perihal); ?></td>
                                <td><?php echo e($penawaran->customer); ?></td>
                                <td><?php echo e($penawaran->created_at->format('d/m/Y')); ?></td>
                                <td>
                                    <?php
                                        $statusClass = '';
                                        switch($penawaran->status) {
                                            case 'Draft':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Dikirim ke customer':
                                                $statusClass = 'bg-info';
                                                break;
                                            case 'PO customer':
                                                $statusClass = 'bg-primary';
                                                break;
                                            case 'Proses penyediaan':
                                                $statusClass = 'bg-secondary';
                                                break;
                                            case 'Selesai':
                                                $statusClass = 'bg-success';
                                                break;
                                        }
                                    ?>
                                    <span class="badge <?php echo e($statusClass); ?> text-white"><?php echo e($penawaran->status); ?></span>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('warehouse.penawaran.show', $penawaran->id)); ?>" class="btn btn-sm btn-info">
                                        <i class="mdi mdi-eye"></i> Detail & Update Status
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <div class="d-flex justify-content-between align-items-center mt-3">
                    <div>
                        <span>Showing <?php echo e($penawarans->firstItem() ?? 0); ?> to <?php echo e($penawarans->lastItem() ?? 0); ?> of <?php echo e($penawarans->total()); ?> entries</span>
                    </div>
                    <div>
                        <?php echo e($penawarans->links()); ?>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('warehouse.content', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\portalpwb\resources\views/warehouse/penawaran/index.blade.php ENDPATH**/ ?>