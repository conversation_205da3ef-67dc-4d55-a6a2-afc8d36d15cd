<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\DailyReport;
use App\Models\Unit;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UnitScheduleController extends Controller
{
    /**
     * Display the unit schedule page for sites
     */
    public function index()
    {
        return view('unit-schedule.index');
    }

    /**
     * Display the unit schedule page for HO with site filter
     */
    public function indexHO()
    {
        $sites = Site::all();
        return view('unit-schedule.index-ho', compact('sites'));
    }

    /**
     * Get unit schedule data for AJAX requests
     */
    public function getData(Request $request)
    {
        try {
            // Debug session data
            Log::info('Unit Schedule getData - Session data:', [
                'role' => session('role'),
                'site_id' => session('site_id'),
                'request_site_id' => $request->site_id
            ]);

            $query = Unit::with(['site', 'dailyReports' => function($q) {
                $q->whereNotNull('hm')
                  ->orderBy('date_in', 'desc')
                  ->orderBy('created_at', 'desc');
            }]);

            // For site users, filter by their site
            if (session('role') !== 'adminho' && session('site_id')) {
                $query->where('site_id', session('site_id'));
                Log::info('Filtering by site for non-HO user:', ['site_id' => session('site_id')]);
            }

            // For HO users, apply site filter if provided
            if ($request->filled('site_id') && session('role') === 'adminho') {
                $query->where('site_id', $request->site_id);
                Log::info('Filtering by site for HO user:', ['site_id' => $request->site_id]);
            } else if (session('role') === 'adminho') {
                // For HO users without site filter, show all units
                Log::info('HO user - showing all units (no site filter)');
            }

            // Apply search filter
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('unit_code', 'LIKE', "%{$search}%")
                      ->orWhere('unit_type', 'LIKE', "%{$search}%");
                });
            }

            $units = $query->get();
            Log::info('Units retrieved:', ['count' => $units->count(), 'units' => $units->pluck('unit_code', 'id')->toArray()]);
            $scheduleData = [];

            foreach ($units as $unit) {
                $latestReport = $unit->dailyReports->first();
                
                if (!$latestReport) {
                    $scheduleData[] = [
                        'unit_id' => $unit->id,
                        'unit_code' => $unit->unit_code,
                        'unit_type' => $unit->unit_type,
                        'site_name' => $unit->site ? $unit->site->site_name : '-',
                        'current_hm' => '-',
                        'last_service_date' => '-',
                        'next_service_hm' => '-',
                        'remaining_hm' => '-',
                        'days_until_service' => '-',
                        'predicted_service_date' => '-',
                        'status' => 'No Data',
                        'status_class' => 'text-muted',
                        'has_data' => false
                    ];
                    continue;
                }

                $currentHM = $latestReport->hm;
                $lastServiceDate = Carbon::parse($latestReport->date_in);

                // Calculate next service HM (next multiple of 250)
                $nextServiceHM = ceil($currentHM / 250) * 250;
                
                // If current HM is already at a service interval, add 250
                if ($currentHM % 250 == 0) {
                    $nextServiceHM = $currentHM + 250;
                }

                // Calculate remaining HM until next service
                $remainingHM = $nextServiceHM - $currentHM;

                // Calculate days until service (assuming 20 HM per day average usage)
                $averageHMPerDay = 20;
                $daysUntilService = ceil($remainingHM / $averageHMPerDay);

                // Calculate predicted service date
                $predictedServiceDate = $lastServiceDate->copy()->addDays($daysUntilService);

                // Determine status and styling
                $status = 'Normal';
                $statusClass = 'text-success';
                
                if ($daysUntilService <= 7) {
                    $status = 'Due Soon';
                    $statusClass = 'text-secondary';
                } elseif ($daysUntilService <= 0) {
                    $status = 'Overdue';
                    $statusClass = 'text-danger';
                } elseif ($daysUntilService <= 3) {
                    $status = 'Critical';
                    $statusClass = 'text-danger';
                }

                $scheduleData[] = [
                    'unit_id' => $unit->id,
                    'unit_code' => $unit->unit_code,
                    'unit_type' => $unit->unit_type,
                    'site_name' => $unit->site ? $unit->site->site_name : '-',
                    'current_hm' => number_format($currentHM, 0),
                    'last_service_date' => $lastServiceDate->format('d/m/Y'),
                    'next_service_hm' => number_format($nextServiceHM, 0),
                    'remaining_hm' => number_format($remainingHM, 0),
                    'days_until_service' => $daysUntilService,
                    'predicted_service_date' => $predictedServiceDate->format('d/m/Y'),
                    'status' => $status,
                    'status_class' => $statusClass,
                    'has_data' => true
                ];
            }

            // Sort by days until service (ascending, with overdue first)
            usort($scheduleData, function($a, $b) {
                if (!$a['has_data'] && !$b['has_data']) return 0;
                if (!$a['has_data']) return 1;
                if (!$b['has_data']) return -1;
                
                $aDays = is_numeric($a['days_until_service']) ? $a['days_until_service'] : 999;
                $bDays = is_numeric($b['days_until_service']) ? $b['days_until_service'] : 999;
                
                return $aDays <=> $bDays;
            });

            return response()->json([
                'success' => true,
                'data' => $scheduleData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching unit schedule data: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export unit schedule data to Excel
     */
    public function exportExcel(Request $request)
    {
        try {
            // Get the same data as the main view
            $response = $this->getData($request);
            $responseData = json_decode($response->getContent(), true);
            
            if (!$responseData['success']) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to fetch data for export'
                ], 500);
            }

            $scheduleData = $responseData['data'];

            // Create Excel export
            $headers = [
                'Unit Code',
                'Unit Type', 
                'Site',
                'Current HM',
                'Last Service Date',
                'Next Service HM',
                'Remaining HM',
                'Days Until Service',
                'Predicted Service Date',
                'Status'
            ];

            $exportData = [];
            $exportData[] = $headers;

            foreach ($scheduleData as $item) {
                $exportData[] = [
                    $item['unit_code'],
                    $item['unit_type'],
                    $item['site_name'],
                    $item['current_hm'],
                    $item['last_service_date'],
                    $item['next_service_hm'],
                    $item['remaining_hm'],
                    $item['days_until_service'],
                    $item['predicted_service_date'],
                    $item['status']
                ];
            }

            // Create a simple CSV response for now
            $filename = 'unit_schedule_' . date('Y-m-d_H-i-s') . '.csv';
            
            $handle = fopen('php://temp', 'w+');
            foreach ($exportData as $row) {
                fputcsv($handle, $row);
            }
            rewind($handle);
            $csv = stream_get_contents($handle);
            fclose($handle);

            return response($csv)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error exporting data: ' . $e->getMessage()
            ], 500);
        }
    }
}
